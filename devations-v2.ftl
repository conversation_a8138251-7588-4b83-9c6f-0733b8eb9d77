<#-- Deviations Table Template -->
<w:tbl xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
    <w:tblPr>
        <w:tblStyle w:val="TableGrid"/>
        <w:tblW w:w="${styling.table.width}" w:type="${styling.table.widthType}"/>
        <w:tblBorders>
            <w:top w:val="${styling.table.borders.top}"/>
            <w:left w:val="${styling.table.borders.left}"/>
            <w:bottom w:val="${styling.table.borders.bottom}"/>
            <w:right w:val="${styling.table.borders.right}"/>
            <w:insideH w:val="${styling.table.borders.insideH}"/>
            <w:insideV w:val="${styling.table.borders.insideV}"/>
        </w:tblBorders>
    </w:tblPr>
    <w:tblGrid>
        <#list carriers as carrier>
            <w:gridCol w:w="${styling.columns.carrier.width}"/>
        </#list>
    </w:tblGrid>

    <!-- Header Row with "Deviations" title -->
    <w:tr>
        <w:trPr>
            <w:trHeight w:val="${styling.rows.header.height}"/>
        </w:trPr>
        <w:tc>
            <w:tcPr>
                <w:tcW w:w="${styling.rows.header.cell.width}" w:type="dxa"/>
                <w:gridSpan w:val="${carriers?size}"/>
                <w:shd w:val="clear" w:color="auto"
                       w:fill="${styling.rows.section.cell.backgroundColor}"/>
                <w:vAlign w:val="${styling.rows.header.cell.verticalAlign}"/>
            </w:tcPr>
            <w:p>
                <w:pPr>
                    <w:jc w:val="${styling.rows.header.text.alignment}"/>
                </w:pPr>
                <w:r>
                    <w:rPr>
                        <w:color w:val="${styling.rows.header.text.color}"/>
                        <w:sz w:val="${styling.rows.header.text.fontSize}"/>
                        <#if styling.rows.header.text.bold><w:b/></#if>
                    </w:rPr>
                    <w:t>Deviations</w:t>
                </w:r>
            </w:p>
        </w:tc>
    </w:tr>



    <#assign maxDeviations = 0>
    <#list sections as section>
        <#if (section.deviations?size > maxDeviations)>
            <#assign maxDeviations = section.deviations?size>
        </#if>
    </#list>

    <#list 0..<maxDeviations as deviationIndex>
        <w:tr>
            <w:trPr>
                <w:trHeight w:val="${styling.rows.benefit.height}"/>
            </w:trPr>
            <#list carriers as carrier>
                <w:tc>
                    <w:tcPr>
                        <w:tcW w:w="${styling.rows.benefit.carrierCell.width}" w:type="dxa"/>
                        <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign}"/>
                        <#-- Add spacing between deviation rows -->
                        <w:tcMar>
                            <w:top w:w="80" w:type="dxa"/>
                            <w:bottom w:w="80" w:type="dxa"/>
                        </w:tcMar>
                    </w:tcPr>
                    <w:p>
                        <w:pPr>
                            <w:jc w:val="${styling.rows.benefit.carrierText.alignment}"/>
                        </w:pPr>
                        <#assign deviationText = "-">
                        <#assign hasContent = false>
                        <#list sections as section>
                            <#if section.id == carrier && (section.deviations?size > deviationIndex)>
                                <#assign deviationText = section.deviations[deviationIndex]>
                                <#assign hasContent = (deviationText != "-" && deviationText?trim != "")>
                                <#break>
                            </#if>
                        </#list>
                        <#-- Show carrier name in bold as prefix only for the first deviation and only if content exists -->
                        <#if deviationIndex == 0 && hasContent>
                            <w:r>
                                <w:rPr>
                                    <w:color w:val="${styling.rows.benefit.carrierText.color}"/>
                                    <w:sz w:val="${styling.rows.benefit.carrierText.fontSize}"/>
                                    <w:b/>
                                </w:rPr>
                                <w:t>${carrier} : </w:t>
                            </w:r>
                        </#if>
                        <#if hasContent>
                            <w:r>
                                <w:rPr>
                                    <w:color w:val="${styling.rows.benefit.carrierText.color}"/>
                                    <w:sz w:val="${styling.rows.benefit.carrierText.fontSize}"/>
                                    <#if styling.rows.benefit.carrierText.bold><w:b/></#if>
                                </w:rPr>
                                <w:t xml:space="preserve"> ${deviationText?replace('\n', '\n ')}</w:t>
                            </w:r>
                        <#else>
                            <w:r>
                                <w:rPr>
                                    <w:color w:val="${styling.rows.benefit.carrierText.color}"/>
                                    <w:sz w:val="${styling.rows.benefit.carrierText.fontSize}"/>
                                </w:rPr>
                                <w:t>-</w:t>
                            </w:r>
                        </#if>
                    </w:p>
                </w:tc>
            </#list>
        </w:tr>
    </#list>
</w:tbl>